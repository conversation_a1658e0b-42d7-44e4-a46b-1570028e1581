/* Reset and Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
}

body {
  font-family: "Inter", sans-serif;
  line-height: 1.6;
  color: #333;
  overflow-x: hidden;
  background: #ffffff;
}

/* Ensure no white space above header on mobile */
html,
body {
  margin: 0;
  padding: 0;
  background: #ffffff;
}

/* Performance: minimize layout work below the fold */
section:not(:first-of-type) {
  content-visibility: auto;
  contain-intrinsic-size: 1000px;
}

/* Performance: avoid FOIT when web fonts load */
:root {
  --font-inter: "Inter", system-ui, -apple-system, Segoe UI, Roboto, Helvetica,
    Arial, sans-serif;
  --font-serif: "Playfair Display", Georgia, "Times New Roman", Times, serif;
}

body {
  font-family: var(--font-inter);
}

/* Remove bold from all headings and ensure Inter font */
h1,
h2,
h3,
h4,
h5,
h6 {
  font-family: var(--font-inter) !important;
  font-weight: normal !important;
}

/* Preloader Styles */
.preloader {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10000;
  transition: opacity 0.8s ease, visibility 0.8s ease;
}

.preloader.fade-out {
  opacity: 0;
  visibility: hidden;
}

.preloader-content {
  text-align: center;
  color: white;
}

.logo-animation h1 {
  font-family: var(--font-inter);
  font-size: 4rem;
  font-weight: normal;
  margin-bottom: 2rem;
  letter-spacing: 0.2em;
  animation: logoGlow 2s ease-in-out infinite alternate;
}

.loading-bar {
  width: 200px;
  height: 4px;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 2px;
  overflow: hidden;
  margin: 0 auto;
}

.loading-progress {
  height: 100%;
  background: linear-gradient(90deg, #ffd700, #ffed4e, #ffd700);
  border-radius: 2px;
  animation: loadingProgress 2s ease-in-out;
  background-size: 200% 100%;
  animation: loadingProgress 2s ease-in-out, shimmer 1.5s ease-in-out infinite;
}

@keyframes logoGlow {
  0% {
    text-shadow: 0 0 20px rgba(255, 215, 0, 0.5);
  }
  100% {
    text-shadow: 0 0 40px rgba(255, 215, 0, 0.8),
      0 0 60px rgba(255, 215, 0, 0.6);
  }
}

@keyframes loadingProgress {
  0% {
    width: 0%;
  }
  100% {
    width: 100%;
  }
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

/* Header Styles */
.header {
  position: fixed;
  top: 0;
  width: 100%;
  background: rgba(255, 255, 255, 0.85);
  backdrop-filter: blur(10px);
  z-index: 1000;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  transition: background 0.2s ease, box-shadow 0.2s ease;
}
.header.scrolled {
  background: rgba(255, 255, 255, 0.98);
  box-shadow: 0 2px 20px rgba(0, 0, 0, 0.15);
}

.navbar {
  padding: 1rem 0;
}

.nav-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.nav-logo h2 {
  color: #2c5aa0;
  font-weight: normal;
  font-size: 1.5rem;
  margin-bottom: 0.2rem;
  font-family: var(--font-inter);
  letter-spacing: 0.05em;
}

.nav-menu {
  display: flex;
  list-style: none;
  gap: 2rem;
}

.nav-link {
  text-decoration: none;
  color: #333;
  font-weight: normal;
  transition: color 0.3s ease;
  position: relative;
}

.nav-link:hover {
  color: #2c5aa0;
}

.nav-link::after {
  content: "";
  position: absolute;
  width: 0;
  height: 2px;
  bottom: -5px;
  left: 0;
  background-color: #2c5aa0;
  transition: width 0.3s ease;
}

.nav-link:hover::after {
  width: 100%;
}

.nav-contact .phone-link {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #2c5aa0;
  text-decoration: none;
  font-weight: normal;
  padding: 0.5rem 1rem;
  border: 2px solid #2c5aa0;
  border-radius: 25px;
  transition: all 0.3s ease;
}

.nav-contact .phone-link:hover {
  background: #2c5aa0;
  color: white;
}

.hamburger {
  display: none;
  flex-direction: column;
  cursor: pointer;
  padding: 10px;
  border-radius: 4px;
  transition: background-color 0.3s ease;
  min-height: 44px;
  min-width: 44px;
  justify-content: center;
  align-items: center;
}

.hamburger:hover {
  background-color: rgba(44, 90, 160, 0.1);
}

.bar {
  width: 25px;
  height: 3px;
  background: #333;
  margin: 3px 0;
  transition: 0.3s;
  border-radius: 2px;
}

/* Hero Section */
.hero {
  min-height: 100vh;
  display: flex;
  align-items: center;
  position: relative;
  overflow: hidden;
  background: #ffffff;
  padding-top: 80px; /* Account for fixed header */
}

.hero-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}

.hero-pattern {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%),
    radial-gradient(
      circle at 25% 25%,
      rgba(44, 90, 160, 0.05) 0%,
      transparent 50%
    ),
    radial-gradient(
      circle at 75% 75%,
      rgba(255, 215, 0, 0.05) 0%,
      transparent 50%
    );
  background-size: 100% 100%, 800px 800px, 600px 600px;
}

.hero-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(44,90,160,0.03)" stroke-width="1"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
}

.hero-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 20px;
  position: relative;
  z-index: 2;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
  align-items: center;
}

.hero-content {
  text-align: left;
}

/* Mobile override for hero centering */
@media (max-width: 768px) {
  .hero {
    text-align: center !important;
    padding-top: 75px !important;
    min-height: 100vh !important;
    padding-bottom: 2rem !important;
    background: #ffffff !important;
  }

  /* Force white background on mobile and hide complex patterns */
  .hero-background,
  .hero-pattern,
  .hero-overlay {
    display: none !important;
  }

  /* Ensure hero has solid white background */
  .hero::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: #ffffff !important;
    z-index: 1;
  }

  .hero-container {
    text-align: center !important;
    justify-items: center !important;
    padding-top: 0.5rem !important;
    position: relative;
    z-index: 2;
  }

  .hero-content {
    text-align: center !important;
    position: relative;
    z-index: 2;
  }

  .hero-content * {
    text-align: center !important;
  }

  .hero-title {
    margin-top: 0 !important;
    padding-top: 0 !important;
    line-height: 1.2 !important;
  }

  /* Shorter service card descriptions for mobile */
  .service-card {
    height: 360px !important;
  }

  .service-card p {
    display: none !important;
  }

  .service-card-content {
    display: flex !important;
    flex-direction: column !important;
    height: 100% !important;
    justify-content: space-between !important;
  }

  .service-card-content h3 {
    height: 4.5rem !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    text-align: center !important;
    margin-bottom: 1rem !important;
    line-height: 1.3 !important;
  }

  .service-card:nth-child(1) .service-card-content::after {
    content: "High-quality precast concrete products for civil industry.";
    display: block;
    color: #666;
    line-height: 1.6;
    text-align: center;
  }

  .service-card:nth-child(2) .service-card-content::after {
    content: "Custom solutions designed for your specific project needs.";
    display: block;
    color: #666;
    line-height: 1.6;
    text-align: center;
  }

  .service-card:nth-child(3) .service-card-content::after {
    content: "Reliable delivery ensuring products arrive on time.";
    display: block;
    color: #666;
    line-height: 1.6;
    text-align: center;
  }

  .service-card:nth-child(4) .service-card-content::after {
    content: "Professional consultation to help choose the right solutions.";
    display: block;
    color: #666;
    line-height: 1.6;
    text-align: center;
  }
}

.hero-badge {
  display: inline-block;
  background: rgba(44, 90, 160, 0.1);
  color: #2c5aa0;
  padding: 0.5rem 1rem;
  border-radius: 25px;
  font-size: 0.9rem;
  font-weight: 600;
  margin-bottom: 1.5rem;
  border: 1px solid rgba(44, 90, 160, 0.2);
}

.hero-title {
  font-size: 3.5rem;
  font-weight: normal;
  line-height: 1.1;
  margin-bottom: 1.5rem;
  color: #1a202c;
  font-family: var(--font-inter);
}

.hero-title .highlight {
  color: #2c5aa0;
  position: relative;
}

.hero-title .highlight::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 4px;
  background: linear-gradient(90deg, #ffd700, #ffed4e);
  border-radius: 2px;
}

.hero-subtitle {
  font-size: 1.2rem;
  line-height: 1.6;
  color: #64748b;
  margin-bottom: 2rem;
  max-width: 500px;
}

.hero-stats {
  display: flex;
  gap: 2rem;
  margin-bottom: 2rem;
}

.stat-item {
  text-align: center;
}

.stat-number {
  font-size: 2rem;
  font-weight: normal;
  color: #2c5aa0;
  font-family: var(--font-inter);
}

.stat-label {
  font-size: 0.9rem;
  color: #64748b;
  font-weight: 500;
}

.hero-features {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  margin-top: 1.5rem;
  margin-bottom: 2.5rem;
}

.feature-pill {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: white;
  padding: 0.75rem 1.25rem;
  border-radius: 25px;
  border: 1px solid #e2e8f0;
  font-size: 0.9rem;
  font-weight: 600;
  color: #475569;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.feature-pill:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(44, 90, 160, 0.15);
  border-color: #2c5aa0;
}

.feature-pill i {
  color: #2c5aa0;
  font-size: 1rem;
}

/* Hero Visual Card */
.hero-visual {
  display: flex;
  justify-content: center;
  align-items: center;
}

.visual-card {
  background: white;
  border-radius: 20px;
  padding: 2rem;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  border: 1px solid #e2e8f0;
  max-width: 500px;
  width: 100%;
}

.card-header {
  text-align: center;
  margin-bottom: 2rem;
}

.card-header h3 {
  font-size: 1.5rem;
  font-weight: normal;
  color: #2c5aa0;
  font-family: var(--font-inter);
}

.commitment-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1.5rem;
}

.commitment-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  border-radius: 12px;
  transition: all 0.3s ease;
}

.commitment-item:hover {
  background: #f8fafc;
  transform: translateX(5px);
}

.commitment-icon {
  width: 50px;
  height: 50px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.2rem;
  color: white;
}

.commitment-icon.quality {
  background: linear-gradient(135deg, #10b981, #059669);
}

.commitment-icon.availability {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
}

.commitment-icon.reliability {
  background: linear-gradient(135deg, #8b5cf6, #7c3aed);
}

.commitment-icon.affordability {
  background: linear-gradient(135deg, #f59e0b, #d97706);
}

.commitment-text h4 {
  font-size: 1.1rem;
  font-weight: normal;
  color: #1a202c;
  margin-bottom: 0.25rem;
}

.commitment-text p {
  font-size: 0.9rem;
  color: #64748b;
  line-height: 1.4;
}

.hero-cta {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

/* Scroll Indicator */
.scroll-indicator {
  position: absolute;
  bottom: 2rem;
  left: 50%;
  transform: translateX(-50%);
  animation: bounce 2s infinite;
}

.scroll-arrow {
  width: 30px;
  height: 30px;
  border: 2px solid rgba(255, 255, 255, 0.8);
  border-top: none;
  border-right: none;
  transform: rotate(-45deg);
}

@keyframes bounce {
  0%,
  20%,
  50%,
  80%,
  100% {
    transform: translateX(-50%) translateY(0);
  }
  40% {
    transform: translateX(-50%) translateY(-10px);
  }
  60% {
    transform: translateX(-50%) translateY(-5px);
  }
}

/* Button Styles */
.btn {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 1.2rem 2.5rem;
  border-radius: 50px;
  text-decoration: none;
  font-weight: 600;
  transition: all 0.4s ease;
  border: 2px solid transparent;
  cursor: pointer;
  font-size: 1rem;
  position: relative;
  overflow: hidden;
}

.btn::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  transition: left 0.6s ease;
}

.btn:hover::before {
  left: 100%;
}

.btn-primary {
  background: linear-gradient(135deg, #ffd700, #ffed4e);
  color: #333;
  border-color: #ffd700;
  box-shadow: 0 4px 15px rgba(255, 215, 0, 0.3);
}

.btn-primary:hover {
  background: transparent;
  color: #ffd700;
  border-color: #ffd700;
  transform: translateY(-3px);
  box-shadow: 0 15px 35px rgba(255, 215, 0, 0.4);
}

.btn-outline {
  background: #2c5aa0;
  color: white;
  border-color: #2c5aa0;
  box-shadow: 0 8px 25px rgba(44, 90, 160, 0.2);
}

.btn-outline:hover {
  background: #1e3a6f;
  color: white;
  border-color: #1e3a6f;
  transform: translateY(-3px);
  box-shadow: 0 15px 35px rgba(44, 90, 160, 0.4);
}

.btn i {
  transition: transform 0.3s ease;
}

.btn:hover i {
  transform: translateX(5px);
}

/* Section Styles */
section {
  padding: 5rem 0;
}

.section-header {
  text-align: center;
  margin-bottom: 4rem;
}

.section-header h2 {
  font-size: 2.5rem;
  font-weight: normal;
  color: #2c5aa0;
  margin-bottom: 1rem;
  font-family: var(--font-inter);
  position: relative;
}

.section-header h2::after {
  content: "";
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 3px;
  background: linear-gradient(90deg, #ffd700, #ffed4e, #ffd700);
  border-radius: 2px;
}

.section-header p {
  font-size: 1.2rem;
  color: #666;
  max-width: 600px;
  margin: 0 auto;
}

/* About Section */
.about {
  background: #f8f9fa;
}

.about-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
  align-items: start;
}

.about-text {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.about-item h3 {
  color: #2c5aa0;
  font-size: 1.5rem;
  margin-bottom: 1rem;
}

.about-item p {
  color: #666;
  line-height: 1.8;
}

.about-features {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1.5rem;
}

.feature-card {
  background: white;
  padding: 2rem;
  border-radius: 15px;
  text-align: center;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
}

.feature-card:hover {
  transform: translateY(-5px);
}

.feature-card i {
  font-size: 2.5rem;
  color: #2c5aa0;
  margin-bottom: 1rem;
}

.feature-card h4 {
  color: #333;
  font-size: 1.2rem;
  margin-bottom: 0.5rem;
}

.feature-card p {
  color: #666;
  font-size: 0.9rem;
}

/* Services Section */
.services-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 2rem;
}

.service-card {
  background: white;
  padding: 2rem;
  border-radius: 20px;
  text-align: center;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  transition: all 0.4s ease;
  border: 1px solid #eee;
  position: relative;
  overflow: hidden;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.service-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 4px;
  background: linear-gradient(90deg, #2c5aa0, #667eea, #2c5aa0);
  transform: scaleX(0);
  transition: transform 0.4s ease;
}

.service-card:hover::before {
  transform: scaleX(1);
}

.service-card:hover {
  transform: translateY(-15px);
  box-shadow: 0 25px 50px rgba(44, 90, 160, 0.15);
}

.service-card-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.service-card i {
  font-size: 2.5rem;
  color: #2c5aa0;
  margin-bottom: 1.5rem;
  padding: 1rem;
  background: rgba(44, 90, 160, 0.1);
  border-radius: 50%;
  width: 80px;
  height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.service-card:hover i {
  background: #2c5aa0;
  color: white;
  transform: scale(1.1);
}

.service-card h3 {
  color: #333;
  font-size: 1.2rem;
  margin-bottom: 1rem;
  font-weight: normal;
  height: 3rem;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  line-height: 1.3;
}

.service-card p {
  color: #666;
  line-height: 1.6;
  font-size: 0.95rem;
}

/* Gallery Section */
.gallery {
  background: #f8f9fa;
}

.gallery-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
}

.gallery-item {
  position: relative;
  border-radius: 15px;
  overflow: hidden;
  aspect-ratio: 4/3;
  cursor: pointer;
}

.gallery-item img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.gallery-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(44, 90, 160, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
}

/* Only apply hover effects on devices that support hover */
@media (hover: hover) and (pointer: fine) {
  .gallery-item:hover .gallery-overlay {
    opacity: 1;
  }

  .gallery-item:hover img {
    transform: scale(1.1);
  }
}

.gallery-overlay i {
  color: white;
  font-size: 2rem;
}

/* Contact Section */
.contact-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
}

.contact-info {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.contact-item {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
}

.contact-item i {
  font-size: 1.5rem;
  color: #2c5aa0;
  margin-top: 0.2rem;
}

.contact-item h4 {
  color: #333;
  margin-bottom: 0.5rem;
}

.contact-item p {
  color: #666;
  line-height: 1.6;
}

.contact-item a {
  color: #2c5aa0;
  text-decoration: none;
}

.contact-item a:hover {
  text-decoration: underline;
}

.contact-form {
  background: white;
  padding: 2.5rem;
  border-radius: 20px;
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.1);
  position: relative;
  overflow: hidden;
}

.contact-form::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 4px;
  background: linear-gradient(90deg, #2c5aa0, #667eea, #2c5aa0);
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-group input,
.form-group textarea {
  width: 100%;
  padding: 1.2rem;
  border: 2px solid #eee;
  border-radius: 12px;
  font-family: inherit;
  font-size: 1rem;
  transition: all 0.3s ease;
  background: #fafafa;
}

.form-group input:focus,
.form-group textarea:focus {
  outline: none;
  border-color: #2c5aa0;
  background: white;
  box-shadow: 0 0 0 3px rgba(44, 90, 160, 0.1);
  transform: translateY(-2px);
}

.form-group textarea {
  resize: vertical;
  min-height: 120px;
}

/* Footer */
.footer {
  background: #2c5aa0;
  color: white;
  padding: 3rem 0 1rem;
}

.footer-content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
  margin-bottom: 2rem;
  justify-items: start;
}

@media (max-width: 768px) {
  .footer-content {
    justify-items: center;
  }
}

.footer-section {
  text-align: left;
}

.footer-section h3,
.footer-section h4 {
  margin-bottom: 1rem;
  color: #ffd700;
}

.footer-section p {
  margin-bottom: 0.5rem;
  opacity: 0.9;
}

.footer-section i {
  margin-right: 0.5rem;
  color: #ffd700;
}

.footer-section a {
  color: #ffffff;
  text-decoration: none;
  transition: color 0.3s ease;
}

.footer-section a:hover {
  color: #ffd700;
  text-decoration: underline;
}

.footer-bottom {
  text-align: center;
  padding-top: 2rem;
  border-top: 1px solid rgba(255, 255, 255, 0.2);
  opacity: 0.8;
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.footer-bottom p {
  margin: 0;
  width: 100%;
}

/* Animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Logo Link Styles */
.logo-link {
  text-decoration: none;
  color: inherit;
  display: block;
  transition: opacity 0.3s ease;
}

.logo-link:hover {
  opacity: 0.8;
}

.logo-link h2 {
  margin: 0;
}

/* Responsive Design */
/* Extra Small Devices (320px to 480px) */
@media (max-width: 480px) {
  .hamburger {
    display: flex;
  }

  .nav-menu {
    position: fixed;
    left: -100%;
    top: 70px;
    flex-direction: column;
    background-color: white;
    width: 100%;
    text-align: center;
    transition: 0.3s;
    box-shadow: 0 10px 27px rgba(0, 0, 0, 0.05);
    padding: 1.5rem 0;
    z-index: 999;
    max-height: calc(100vh - 70px);
    overflow-y: auto;
  }

  .nav-menu.active {
    left: 0;
  }

  .nav-menu li {
    margin: 0.2rem 0;
  }

  .nav-link {
    font-size: 1.1rem;
    padding: 0.5rem;
    display: block;
    font-weight: normal;
  }

  .nav-contact {
    display: none;
  }

  .navbar {
    padding: 0.8rem 0;
  }
  .container {
    padding: 0 15px;
  }

  .nav-container {
    padding: 0 15px;
  }

  .nav-logo h2 {
    font-size: 1.2rem;
  }

  .hero {
    padding-top: 75px;
    min-height: 100vh;
    text-align: center;
    padding-bottom: 2rem;
  }

  .hero-container {
    text-align: center !important;
    padding: 0 20px;
    margin: 0 auto;
    width: 100%;
  }

  .hero-content {
    text-align: center !important;
    width: 100%;
    margin: 0 auto;
  }

  .hero-title {
    font-size: 2rem;
    line-height: 1.2;
    text-align: center !important;
    margin: 0 auto 1rem auto;
    width: 100%;
  }

  .hero-subtitle {
    font-size: 1rem;
    margin: 0 auto 1.5rem auto;
    text-align: center !important;
    width: 100%;
  }

  .hero-stats {
    justify-content: center !important;
    text-align: center !important;
    margin: 0 auto;
    width: 100%;
  }

  .stat-item {
    text-align: center !important;
  }

  .hero-features {
    justify-content: center !important;
    text-align: center !important;
    margin: 1.5rem auto 2.5rem auto;
    width: 100%;
  }

  .btn {
    padding: 1rem 1.5rem;
    font-size: 0.9rem;
    min-height: 44px;
    min-width: 44px;
  }

  .hero-cta {
    flex-direction: column;
    gap: 1rem;
    text-align: center !important;
    justify-content: center !important;
    align-items: center !important;
    margin: 0 auto;
    width: 100%;
  }

  .hero-cta .btn {
    width: 100%;
    max-width: 280px;
    justify-content: center !important;
    text-align: center !important;
    margin: 0 auto;
  }

  .feature-pill {
    font-size: 0.8rem;
    padding: 0.5rem 1rem;
  }

  /* Hide Our Commitment section on mobile */
  .hero-visual {
    display: none !important;
  }

  .section-header {
    text-align: center;
  }

  .section-header h2 {
    font-size: 1.8rem;
    text-align: center;
  }

  .section-header p {
    font-size: 0.9rem;
    text-align: center;
  }

  section {
    padding: 2.5rem 0;
    text-align: center;
  }

  .about-content {
    display: flex !important;
    flex-direction: column;
    gap: 2rem;
    text-align: center;
  }

  .about-text {
    text-align: center;
  }

  .about-features {
    grid-template-columns: 1fr !important;
    gap: 1rem;
    text-align: center;
  }

  .about-item {
    margin-bottom: 1.5rem;
    text-align: center;
  }

  .about-item h3 {
    font-size: 1.1rem;
    text-align: center;
  }

  .about-item p {
    font-size: 0.9rem;
    text-align: center;
  }

  /* Improve services section spacing on mobile */
  .services {
    padding-bottom: 4rem !important;
  }

  .services-grid {
    gap: 1rem;
    text-align: center;
    margin-bottom: 2rem;
  }

  .service-card {
    padding: 1.5rem;
    text-align: center;
  }

  .service-card h3 {
    text-align: center;
  }

  .service-card p {
    text-align: center;
  }

  /* Add extra bottom padding to last two service cards on mobile */
  .services-grid .service-card:nth-child(3),
  .services-grid .service-card:nth-child(4) {
    margin-bottom: 3rem !important;
    padding-bottom: 2rem !important;
  }

  .gallery-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
    text-align: center;
    justify-items: center;
    overflow-x: hidden; /* Prevent horizontal scrolling */
    width: 100%;
  }

  .contact-content {
    display: flex !important;
    flex-direction: column !important;
    gap: 2rem !important;
    text-align: center;
  }

  .contact-info {
    order: 1;
    width: 100%;
    text-align: center;
  }

  .contact-item {
    text-align: center;
    justify-content: center;
    flex-direction: column !important;
    align-items: center !important;
    gap: 0.5rem !important;
  }

  .contact-item i {
    margin-top: 0 !important;
    margin-bottom: 0.5rem;
    font-size: 2rem !important;
  }

  .contact-item h4 {
    text-align: center;
    margin-bottom: 0.3rem !important;
  }

  .contact-item p {
    text-align: center;
  }

  .contact-form {
    order: 2;
    width: 100%;
    padding: 1.5rem;
    text-align: center;
  }

  .form-group input,
  .form-group textarea {
    padding: 1rem;
    font-size: 0.9rem;
    min-height: 44px;
  }

  .footer-content {
    gap: 1.5rem;
    grid-template-columns: 1fr;
    text-align: center;
    justify-items: center;
  }

  .footer-section {
    text-align: center;
    max-width: 300px;
  }
}

/* Small to Medium Mobile Devices (481px to 768px) */
@media (min-width: 481px) and (max-width: 768px) {
  .hamburger {
    display: flex;
  }

  .nav-menu {
    position: fixed;
    left: -100%;
    top: 80px;
    flex-direction: column;
    background-color: white;
    width: 100%;
    text-align: center;
    transition: 0.3s;
    box-shadow: 0 10px 27px rgba(0, 0, 0, 0.05);
    padding: 2rem 0;
    z-index: 999;
    max-height: calc(100vh - 80px);
    overflow-y: auto;
  }

  .nav-menu.active {
    left: 0;
  }

  .nav-menu li {
    margin: 0.2rem 0;
  }

  .nav-contact {
    display: none;
  }

  .hero {
    padding-top: 75px;
    padding-bottom: 2rem;
  }

  .hero-container {
    grid-template-columns: 1fr;
    gap: 2rem;
    text-align: center !important;
    padding: 2rem 20px;
    margin: 0 auto;
    width: 100%;
  }

  .hero-content {
    text-align: center !important;
    width: 100%;
    margin: 0 auto;
  }

  .hero-title {
    font-size: 2.5rem;
    line-height: 1.2;
    text-align: center;
  }

  .hero-subtitle {
    font-size: 1.1rem;
    text-align: center;
  }

  .hero-features {
    justify-content: center;
    gap: 0.8rem;
    margin-top: 1.5rem;
  }

  .hero-cta {
    justify-content: center;
    gap: 1rem;
  }

  .btn {
    min-height: 44px;
    min-width: 44px;
  }

  .visual-card {
    max-width: 400px;
    margin: 0 auto;
  }

  .about-content {
    grid-template-columns: 1fr !important;
    gap: 2rem;
    display: flex;
    flex-direction: column;
    text-align: center;
  }

  .about-text {
    text-align: center;
  }

  .about-item {
    text-align: center;
  }

  .about-features {
    grid-template-columns: 1fr !important;
    gap: 1rem;
    text-align: center;
  }

  .services-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
    text-align: center;
  }

  .service-card {
    text-align: center;
  }

  .gallery-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
  }

  .contact-content {
    display: flex !important;
    flex-direction: column !important;
    gap: 2rem !important;
  }

  .contact-info {
    order: 1;
    width: 100%;
  }

  .contact-item {
    flex-direction: column !important;
    align-items: center !important;
    text-align: center;
    gap: 0.5rem !important;
  }

  .contact-item i {
    margin-top: 0 !important;
    margin-bottom: 0.5rem;
    font-size: 2rem !important;
  }

  .contact-item h4 {
    margin-bottom: 0.3rem !important;
  }

  .contact-form {
    order: 2;
    width: 100%;
  }

  .footer-content {
    grid-template-columns: repeat(2, 1fr);
    text-align: center;
    gap: 2rem;
    justify-items: center;
  }

  .footer-section {
    text-align: center;
  }

  section {
    padding: 3rem 0;
  }

  .section-header h2 {
    font-size: 2rem;
  }
}

/* Large Mobile/Small Tablet (769px to 1024px) */
@media (min-width: 769px) and (max-width: 1024px) {
  .hero-container {
    gap: 3rem;
  }

  .services-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
  }

  .gallery-grid {
    grid-template-columns: repeat(3, 1fr);
  }

  .contact-content {
    gap: 3rem;
  }
}

/* Additional Mobile Improvements */
@media (max-width: 768px) {
  /* Fix white space above header on mobile and match status bar */
  html,
  body {
    background: rgba(255, 255, 255, 0.85) !important;
    margin: 0 !important;
    padding: 0 !important;
    min-height: 100vh !important;
  }

  /* Remove any potential browser default spacing */
  html {
    padding-top: 0 !important;
    margin-top: 0 !important;
    /* Make status bar area match header */
    background: rgba(255, 255, 255, 0.85) !important;
  }

  /* Ensure the viewport area above header matches */
  body::before {
    content: "";
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    height: 50px;
    background: rgba(255, 255, 255, 0.85);
    z-index: 999;
    pointer-events: none;
  }

  /* Ensure header starts at very top and matches body background */
  .header {
    top: 0 !important;
    margin-top: 0 !important;
    background: rgba(255, 255, 255, 0.85) !important;
    left: 0 !important;
    right: 0 !important;
  }

  /* Ensure scrolled header also matches */
  .header.scrolled {
    background: rgba(255, 255, 255, 0.85) !important;
  }

  /* Clean mobile navigation hover effects */
  .nav-link::after {
    display: none !important;
  }

  .nav-link {
    padding: 0.6rem 1.5rem !important;
    margin: 0.1rem 1rem !important;
    border-radius: 8px !important;
    transition: all 0.3s ease !important;
    font-weight: normal !important;
  }

  .nav-link:hover {
    background-color: rgba(44, 90, 160, 0.1) !important;
    color: #2c5aa0 !important;
  }

  /* Shift hero content up and hide visual section */
  .hero {
    padding-top: 60px !important;
    margin-top: -10px !important;
  }

  .hero-visual {
    display: none !important;
  }

  /* Ensure hero content is perfectly centered when visual is hidden */
  .hero-container {
    display: flex !important;
    flex-direction: column !important;
    justify-content: center !important;
    align-items: center !important;
    text-align: center !important;
    gap: 2rem !important;
  }

  .hero-content {
    width: 100% !important;
    max-width: 600px !important;
    margin: 0 auto !important;
    text-align: center !important;
  }

  /* Fix About section layout for mobile */
  .about-content {
    display: flex !important;
    flex-direction: column !important;
    gap: 2rem !important;
    text-align: center !important;
  }

  .about-text {
    width: 100%;
    order: 1;
    text-align: center;
  }

  .about-item {
    text-align: center;
  }

  .about-item h3 {
    text-align: center;
  }

  .about-item p {
    text-align: center;
  }

  .about-features {
    width: 100%;
    order: 2;
    display: grid !important;
    grid-template-columns: 1fr !important;
    gap: 1rem !important;
    text-align: center;
  }

  /* Fix Contact section layout for mobile */
  .contact-content {
    display: flex !important;
    flex-direction: column !important;
    gap: 2rem !important;
    text-align: center !important;
  }

  .contact-info {
    order: 1;
    width: 100%;
    text-align: center;
  }

  .contact-item {
    text-align: center;
    justify-content: center;
    flex-direction: column !important;
    align-items: center !important;
    gap: 0.5rem !important;
  }

  .contact-item i {
    margin-top: 0 !important;
    margin-bottom: 0.5rem;
    font-size: 2rem !important;
  }

  .contact-item h4 {
    text-align: center;
    margin-bottom: 0.3rem !important;
  }

  .contact-item p {
    text-align: center;
  }

  .contact-form {
    order: 2;
    width: 100%;
    text-align: center;
  }

  /* Ensure all touch targets are at least 44px */
  .nav-link,
  .btn,
  .gallery-item,
  .service-card,
  .feature-card,
  .contact-item a,
  .phone-link {
    min-height: 44px;
    min-width: 44px;
  }

  /* Improve form accessibility */
  .form-group input,
  .form-group textarea,
  .form-group button {
    min-height: 48px;
    font-size: 16px; /* Prevents zoom on iOS */
  }

  /* Better spacing for mobile */
  .hero-features {
    gap: 0.5rem;
  }

  .feature-pill {
    padding: 0.6rem 1rem;
    font-size: 0.85rem;
  }

  /* Improve gallery on mobile */
  .gallery-item {
    min-height: 200px;
    max-width: 100%;
    width: 100%;
    box-sizing: border-box;
    /* Prevent scroll issues on mobile */
    -webkit-transform: translateZ(0);
    transform: translateZ(0);
    will-change: auto;
  }

  /* Prevent touch callouts and selections that can interfere with scrolling */
  .gallery-item,
  .gallery-item img,
  .gallery-overlay {
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    -webkit-tap-highlight-color: transparent;
  }

  /* Ensure gallery section doesn't interfere with scrolling */
  .gallery {
    -webkit-overflow-scrolling: touch;
    overflow: visible;
  }

  .gallery-grid {
    overflow: visible;
  }

  /* Better contact section */
  .contact-item {
    padding: 1.5rem 0;
    border-bottom: 1px solid #eee;
    flex-direction: column !important;
    align-items: center !important;
    text-align: center;
    gap: 0.5rem !important;
  }

  .contact-item:last-child {
    border-bottom: none;
  }

  .contact-item i {
    margin-top: 0 !important;
    margin-bottom: 0.5rem;
    font-size: 2rem !important;
    color: #2c5aa0;
  }

  .contact-item h4 {
    margin-bottom: 0.3rem !important;
    font-size: 1.1rem;
  }

  .contact-item p {
    margin: 0;
    font-size: 0.95rem;
  }

  /* Improve footer spacing */
  .footer-section {
    margin-bottom: 2rem;
  }

  .footer-section:last-child {
    margin-bottom: 0;
  }

  /* Fix text overflow */
  .hero-title,
  .section-header h2 {
    word-wrap: break-word;
    hyphens: auto;
  }

  /* Improve button spacing */
  .hero-cta {
    margin-top: 2rem;
  }

  /* Better visual card on mobile */
  .visual-card {
    padding: 1.5rem;
    margin: 1rem 0;
    text-align: center;
  }

  .visual-card h3 {
    text-align: center;
  }

  .visual-card p {
    text-align: center;
  }

  /* Ensure about section is properly stacked */
  .about-content {
    display: flex !important;
    flex-direction: column !important;
  }

  .about-text {
    order: 1;
  }

  .about-features {
    order: 2;
    display: grid !important;
    grid-template-columns: 1fr !important;
  }

  .feature-card {
    margin-bottom: 1rem;
  }

  /* Improve service cards */
  .service-card {
    margin-bottom: 1rem;
  }

  /* Fix horizontal scrolling */
  body {
    overflow-x: hidden;
  }

  .hero-container,
  .container {
    max-width: 100%;
    overflow-x: hidden;
  }
}

/* Portrait mobile - ensure content fits */
@media (max-width: 768px) and (orientation: portrait) {
  .hero {
    padding-top: 80px !important;
    min-height: 100vh !important;
    display: flex !important;
    align-items: flex-start !important;
    padding-bottom: 2rem !important;
  }

  .hero-container {
    margin-top: 0.5rem !important;
    width: 100% !important;
  }

  .hero-title {
    font-size: 1.8rem !important;
    line-height: 1.2 !important;
    margin-bottom: 1rem !important;
  }
}

/* Landscape orientation fixes */
@media (max-width: 768px) and (orientation: landscape) {
  .hero {
    min-height: 100vh;
    padding-top: 60px;
  }

  .hero-title {
    font-size: 2rem;
  }

  .hero-subtitle {
    font-size: 1rem;
  }

  section {
    padding: 2rem 0;
  }
}

/* High DPI/Retina Display Optimizations */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .btn,
  .nav-link,
  .gallery-item {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }
}

/* Accessibility Improvements */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* Force white menu regardless of system dark mode preference */
@media (prefers-color-scheme: dark) and (max-width: 768px) {
  .nav-menu {
    background-color: white !important;
    color: #333 !important;
  }

  .nav-link {
    color: #333 !important;
  }
}

/* Print styles */
@media print {
  .header,
  .nav-menu,
  .hamburger,
  .hero-cta,
  .gallery,
  .contact-form {
    display: none !important;
  }

  .hero {
    padding-top: 0;
    min-height: auto;
  }

  body {
    font-size: 12pt;
    line-height: 1.4;
  }
}

/* Tablet responsive */
@media (max-width: 1024px) {
  .services-grid {
    grid-template-columns: repeat(2, 1fr);

    gap: 1.5rem;
  }
}

/* Back to Top Button - Mobile Only */
.back-to-top {
  display: none; /* Hidden by default */
  position: fixed;
  bottom: 24px;
  right: 24px;
  width: 44px;
  height: 44px;
  background: rgba(255, 255, 255, 0.95);
  color: #333;
  border: 1px solid rgba(0, 0, 0, 0.08);
  border-radius: 50%;
  cursor: pointer;
  z-index: 1000;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  transition: all 0.2s ease;
  font-size: 16px;
  opacity: 0;
  transform: translateY(10px);
}

.back-to-top.show {
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 1;
  transform: translateY(0);
}

.back-to-top:hover {
  background: rgba(255, 255, 255, 1);
  color: #000;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transform: translateY(-1px);
}

.back-to-top:active {
  transform: translateY(0);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.12);
}

/* Show back-to-top button only on mobile devices */
@media (max-width: 768px) {
  .back-to-top {
    display: flex;
    align-items: center;
    justify-content: center;
  }
}

/* Hide on desktop */
@media (min-width: 769px) {
  .back-to-top {
    display: none !important;
  }
}
